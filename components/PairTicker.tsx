"use client";

import { usePairContext } from "@/app/trade/[symbol]/provider";
import { StarActiveIcon, StarIcon } from "@/assets/icons";
import { getPriceStyle } from "@/utils/helper";
import { memo, useEffect, useMemo } from "react";
import { useSelector, useDispatch } from "react-redux";
import { RootState } from "@/store";
import { PairSymbol } from "./PairSymbol";
import {
  toggleFavoritePair,
  loadFavoritesFromStorage,
} from "@/store/favorites.store";
import AppNumber from "./AppNumber";
import { useMultipleTickers, useSingleTicker } from "@/hooks/useTicker";
import { formatNumber } from "../utils/format";
import { convertToUSDTPrice } from "@/utils/currency";

export const PairTicker = memo(() => {
  const dispatch = useDispatch();
  const { symbol, pairSetting } = usePairContext();
  const { favoritePairs } = useSelector((state: RootState) => state.favorites);

  const { ticker } = useSingleTicker(symbol);
  const { tickers } = useMultipleTickers();

  const isFavorite = favoritePairs.includes(symbol?.toUpperCase());

  useEffect(() => {
    dispatch(loadFavoritesFromStorage());
  }, [dispatch]);

  const handleToggleFavorite = () => {
    if (symbol) {
      dispatch(toggleFavoritePair(symbol));
    }
  };

  // Calculate USD equivalent price
  const usdPrice = useMemo(() => {
    return convertToUSDTPrice(
      ticker?.lastPrice,
      pairSetting?.quoteAsset,
      tickers
    );
  }, [ticker?.lastPrice, pairSetting?.quoteAsset, tickers]);

  useEffect(() => {
    document.title = `${formatNumber(
      ticker?.lastPrice || 0,
      pairSetting?.pricePrecision,
      false
    )} | ${pairSetting?.baseAsset?.toUpperCase()}
          ${pairSetting?.quoteAsset?.toUpperCase()} | ${pairSetting?.baseAsset?.toUpperCase()} to
          ${pairSetting?.quoteAsset?.toUpperCase()} - VDAX`;
  }, [
    ticker?.lastPrice,
    pairSetting?.baseAsset,
    pairSetting?.quoteAsset,
    pairSetting?.pricePrecision,
  ]);

  return (
    <div className="border-white-100 bg-black-900 flex h-auto justify-between gap-8 border-b px-4 py-2 lg:h-[60px] lg:items-center lg:justify-start lg:bg-transparent">
      <div className="flex flex-col gap-1 lg:flex-row lg:gap-8">
        <div className="flex items-center gap-3">
          <div
            onClick={handleToggleFavorite}
            className="border-white-100 hidden cursor-pointer rounded-[4px] border p-1.5 lg:block"
          >
            {isFavorite ? <StarActiveIcon /> : <StarIcon />}
          </div>
          <PairSymbol />
        </div>

        <div className="flex flex-col gap-1">
          <div
            className="heading-lg-medium-24"
            style={{
              color: getPriceStyle(ticker?.latestChange || "0"),
            }}
          >
            <AppNumber
              value={ticker?.lastPrice || 0}
              decimals={pairSetting?.pricePrecision}
              isFormatLargeNumber={false}
            />
          </div>
          <div className="text-white-500">
            <AppNumber
              value={usdPrice || 0}
              decimals={pairSetting?.pricePrecision}
              isForUSD
              isFormatLargeNumber={false}
            />
          </div>
        </div>

        <div
          className="body-sm-medium-12 flex gap-1 lg:hidden"
          style={{ color: getPriceStyle(ticker?.priceChange) }}
        >
          <div className="flex">
            <AppNumber
              value={ticker?.priceChange || 0}
              decimals={pairSetting?.pricePrecision}
              isFormatLargeNumber={false}
            />
          </div>
          <div className="flex">
            <AppNumber
              value={ticker?.priceChangePercent || 0}
              decimals={2}
              isFormatLargeNumber={false}
            />
            %
          </div>
        </div>
      </div>

      <div className="flex gap-4 lg:gap-6">
        <div className="hidden flex-col gap-1.5 lg:flex">
          <div className="body-sm-regular-12 text-white-500">24h change</div>
          <div
            className="body-sm-medium-12 flex items-center gap-1"
            style={{ color: getPriceStyle(ticker?.priceChange) }}
          >
            <div className="flex">
              <AppNumber
                value={ticker?.priceChange || 0}
                decimals={pairSetting?.pricePrecision}
                isFormatLargeNumber={false}
              />
            </div>
            <div className="flex">
              <AppNumber
                value={ticker?.priceChangePercent || 0}
                decimals={2}
                isFormatLargeNumber={false}
                isForPercent
              />
            </div>
          </div>
        </div>

        <div className="flex flex-col gap-3 lg:flex-row lg:gap-6">
          <div className="flex flex-col gap-1.5">
            <div className="body-sm-regular-12 text-white-500">24h High</div>
            <div className="body-sm-medium-12 text-white-900">
              <AppNumber
                value={ticker?.highPrice || 0}
                decimals={pairSetting?.pricePrecision}
                isFormatLargeNumber={false}
              />
            </div>
          </div>

          <div className="flex flex-col gap-1.5">
            <div className="body-sm-regular-12 text-white-500">24h Low</div>
            <div className="body-sm-medium-12 text-white-900">
              <AppNumber
                value={ticker?.lowPrice || 0}
                decimals={pairSetting?.pricePrecision}
                isFormatLargeNumber={false}
              />
            </div>
          </div>
        </div>

        <div className="flex flex-col gap-3 lg:flex-row lg:gap-6 ">
          <div className="flex flex-col gap-1.5">
            <div className="body-sm-regular-12 text-white-500">
              24h Volume ({pairSetting?.baseAsset?.toUpperCase()})
            </div>
            <div className="body-sm-medium-12 text-white-900">
              <AppNumber
                value={ticker?.baseVolume || 0}
                decimals={pairSetting?.quantityPrecision}
                isFormatLargeNumber={false}
              />
            </div>
          </div>

          <div className="flex flex-col gap-1.5">
            <div className="body-sm-regular-12 text-white-500">
              24h Volume ({pairSetting?.quoteAsset?.toUpperCase()})
            </div>
            <div className="body-sm-medium-12 text-white-900">
              <AppNumber
                value={ticker?.quoteVolume || 0}
                decimals={pairSetting?.pricePrecision}
                isFormatLargeNumber={false}
              />
            </div>
          </div>
        </div>
      </div>
    </div>
  );
});

PairTicker.displayName = "PairTicker";
