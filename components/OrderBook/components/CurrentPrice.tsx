import { usePairContext } from "@/app/trade/[symbol]/provider";
import { ArrowDown } from "@/assets/icons";
import AppNumber from "@/components/AppNumber";
import { RootState } from "@/store";
import { getPriceStyle } from "@/utils/helper";
import React from "react";
import { useSelector } from "react-redux";

const CurrentPrice = () => {
  const { pairSetting } = usePairContext();
  const { ticker } = useSelector((state: RootState) => state.ticker);

  return (
    <div className="flex items-center gap-1 p-2">
      <div
        className="heading-lg-medium-24"
        style={{
          color: getPriceStyle(ticker.latestChange || "0"),
        }}
      >
        <AppNumber
          value={ticker?.lastPrice || 0}
          decimals={pairSetting?.pricePrecision}
          isFormatLargeNumber={false}
          className="!font-rotobo-mono"
        />
      </div>
      <ArrowDown
        style={{
          color: getPriceStyle(ticker.latestChange || "0"),
          transform: ticker.isUp && "rotate(180deg)",
        }}
      />

      <div className="body-sm-regular-12 text-white-500">
        <AppNumber
          value={ticker?.lastPrice || 0}
          decimals={pairSetting?.pricePrecision}
          isForUSD
          isFormatLargeNumber={false}
          className="!font-rotobo-mono"
        />
      </div>
    </div>
  );
};

export default CurrentPrice;
