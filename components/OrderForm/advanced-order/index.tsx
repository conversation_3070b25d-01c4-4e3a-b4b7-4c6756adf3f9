"use client";

import { EOrderType } from "..";
import React, { useState } from "react";
import { EOrderSide } from "../OrderFormMobile";
import { Ticker } from "@/types/pair";
import { BuyOrder } from "../components/BuyOrder";
import { SellOrder } from "../components/SellOrder";

type TAdvancedOrder = {
  orderType: EOrderType;
  initTicker: Ticker | null;
};

export const AdvancedOrder = ({ orderType, initTicker }: TAdvancedOrder) => {
  const [orderSide, setOrderSide] = useState<EOrderSide>(EOrderSide.BUY);

  return (
    <div className="p-4">
      <div className="bg-white-50 mb-2.5 grid grid-cols-2 rounded-[4px] p-[2px]">
        <div
          className="bg-white-100 flex w-full cursor-pointer rounded-l-[4px]"
          onClick={() => setOrderSide(EOrderSide.BUY)}
        >
          <div
            className={`body-sm-semibold-12 flex flex-1 items-center justify-center rounded-l-[4px] text-center ${
              orderSide === EOrderSide.BUY ? "bg-[color:var(--up-color)]" : ""
            } `}
          >
            Buy
          </div>
          {orderSide === EOrderSide.BUY && (
            <div className="border-y-12 h-0 w-0 border-l-[12px] border-y-transparent border-l-[color:var(--up-color)]" />
          )}
        </div>
        <div
          className="bg-white-100 flex w-full cursor-pointer rounded-r-[4px]"
          onClick={() => setOrderSide(EOrderSide.SELL)}
        >
          {orderSide === EOrderSide.SELL && (
            <div className="border-y-12 h-0 w-0 border-r-[12px] border-y-transparent border-r-[color:var(--down-color)]" />
          )}

          <div
            className={`body-sm-semibold-12 flex flex-1 items-center justify-center rounded-r-[4px] text-center ${
              orderSide === EOrderSide.SELL ? "bg-[color:var(--down-color)]" : ""
            } `}
          >
            Sell
          </div>
        </div>
      </div>

      {orderSide === EOrderSide.BUY ? (
        <BuyOrder orderType={orderType} initTicker={initTicker} />
      ) : (
        <SellOrder orderType={orderType} initTicker={initTicker} />
      )}
    </div>
  );
};
